/**
 * Enhanced polling strategy for YouCam API with exponential backoff and better timeout handling
 */
class PollingStrategy {
  constructor(options = {}) {
    this.config = {
      // Initial polling interval (1 second)
      initialInterval: options.initialInterval || 1000,
      // Maximum polling interval (10 seconds)
      maxInterval: options.maxInterval || 10000,
      // Maximum total polling time (2 minutes)
      maxTotalTime: options.maxTotalTime || 120000,
      // Maximum number of attempts
      maxAttempts: options.maxAttempts || 60,
      // Backoff multiplier
      backoffMultiplier: options.backoffMultiplier || 1.5,
      // Fast polling duration (first 30 seconds with shorter intervals)
      fastPollingDuration: options.fastPollingDuration || 30000,
      // Fast polling interval (1 second)
      fastPollingInterval: options.fastPollingInterval || 1000
    };
  }

  /**
   * Calculate the next polling interval based on attempt number and elapsed time
   */
  getNextInterval(attemptNumber, elapsedTime) {
    // Use fast polling for the first period
    if (elapsedTime < this.config.fastPollingDuration) {
      return this.config.fastPollingInterval;
    }

    // Calculate exponential backoff interval
    const exponentialInterval = this.config.initialInterval * 
      Math.pow(this.config.backoffMultiplier, attemptNumber - 1);

    // Cap at maximum interval
    return Math.min(exponentialInterval, this.config.maxInterval);
  }

  /**
   * Check if polling should continue
   */
  shouldContinuePolling(attemptNumber, elapsedTime) {
    return attemptNumber < this.config.maxAttempts && 
           elapsedTime < this.config.maxTotalTime;
  }

  /**
   * Get progress information for logging
   */
  getProgressInfo(attemptNumber, elapsedTime) {
    const totalTimeSeconds = (elapsedTime / 1000).toFixed(1);
    const remainingTime = Math.max(0, (this.config.maxTotalTime - elapsedTime) / 1000).toFixed(1);
    const progressPercent = Math.min(100, (elapsedTime / this.config.maxTotalTime * 100)).toFixed(1);

    return {
      attempt: attemptNumber,
      elapsedSeconds: totalTimeSeconds,
      remainingSeconds: remainingTime,
      progressPercent: progressPercent,
      phase: elapsedTime < this.config.fastPollingDuration ? 'fast' : 'exponential'
    };
  }

  /**
   * Generate timeout error message with helpful information
   */
  generateTimeoutMessage(attemptNumber, elapsedTime, lastResponse = null) {
    const totalTimeSeconds = (elapsedTime / 1000).toFixed(1);
    
    let message = `Face analysis timeout after ${totalTimeSeconds}s (${attemptNumber} attempts).\n\n`;
    
    message += 'This usually indicates one of the following issues:\n';
    message += '• No clear face was detected in the image\n';
    message += '• Image quality is too low for reliable analysis\n';
    message += '• Face is too small, blurry, or at an extreme angle\n';
    message += '• Image contains multiple faces or partial faces\n';
    message += '• Lighting conditions are poor\n\n';
    
    message += 'Recommendations:\n';
    message += '• Use a clear, well-lit photo with a single face\n';
    message += '• Ensure the face takes up at least 20% of the image\n';
    message += '• Avoid extreme angles, shadows, or obstructions\n';
    message += '• Use higher resolution images (minimum 300x300px)\n';
    message += '• Ensure good contrast between face and background';

    if (lastResponse) {
      message += `\n\nLast API response: ${JSON.stringify(lastResponse, null, 2)}`;
    }

    return message;
  }

  /**
   * Sleep for specified milliseconds
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Enhanced polling with progress callbacks
   */
  async poll(pollingFunction, options = {}) {
    const startTime = Date.now();
    let attemptNumber = 0;
    let lastResponse = null;
    
    const onProgress = options.onProgress || (() => {});
    const onAttempt = options.onAttempt || (() => {});

    while (true) {
      attemptNumber++;
      const elapsedTime = Date.now() - startTime;
      
      // Check if we should continue polling
      if (!this.shouldContinuePolling(attemptNumber, elapsedTime)) {
        throw new Error(this.generateTimeoutMessage(attemptNumber, elapsedTime, lastResponse));
      }

      try {
        // Call the polling function
        onAttempt(attemptNumber, elapsedTime);
        const result = await pollingFunction();
        lastResponse = result;

        // Check if we got a successful result
        if (result && result.success) {
          return result;
        }

        // Check for specific status responses
        if (result && result.status === 'success') {
          return result;
        }

        if (result && (result.status === 'error' || result.status === 'failed')) {
          throw new Error(`Analysis failed: ${result.error_message || result.message || 'Unknown error'}`);
        }

        // Continue polling if status is 'running' or similar
        const progressInfo = this.getProgressInfo(attemptNumber, elapsedTime);
        onProgress(progressInfo, result);

        // Calculate next interval and wait
        const nextInterval = this.getNextInterval(attemptNumber, elapsedTime);
        await this.sleep(nextInterval);

      } catch (error) {
        // If it's a polling timeout, re-throw
        if (error.message.includes('timeout after')) {
          throw error;
        }

        // For other errors, check if we should retry or fail immediately
        if (error.message.includes('Invalid Task Id') || 
            error.message.includes('HTTP 400') ||
            error.message.includes('HTTP 401') ||
            error.message.includes('HTTP 403')) {
          // Don't retry for these errors
          throw error;
        }

        // For network errors or temporary issues, continue polling
        console.warn(`Polling attempt ${attemptNumber} failed, retrying:`, error.message);
        lastResponse = { error: error.message };
        
        const nextInterval = this.getNextInterval(attemptNumber, elapsedTime);
        await this.sleep(nextInterval);
      }
    }
  }
}

module.exports = PollingStrategy;
