const express = require('express');
const router = express.Router();
const {
  uploadImage,
  startAnalysis,
  getAnalysisResult,
  analyzeFace,
  createTestTask,
  debugCompleteFlow,
  analyzeFaceDetectionIssues,
  testSkinConditionImage
} = require('../controllers/faceController');

router.post('/upload', uploadImage);           // Step 1: Upload image, returns fileId
router.post('/start-analysis', startAnalysis); // Step 2: Start analysis with fileId, returns taskId
router.get('/result/:taskId', getAnalysisResult); // Step 3: Get result with taskId

// Test endpoint to create a fresh task ID for debugging
router.post('/create-test-task', createTestTask);

// Enhanced debug endpoint to test complete flow with detailed analysis
router.post('/debug-flow', debugCompleteFlow);

// Specialized endpoint to analyze face detection issues
router.post('/analyze-face-issues', analyzeFaceDetectionIssues);

// Specialized endpoint for testing skin condition images
router.post('/test-skin-condition', testSkinConditionImage);

// // Legacy combined endpoint (deprecated but maintained for backward compatibility)
router.post('/analyze', analyzeFace);

module.exports = router;
