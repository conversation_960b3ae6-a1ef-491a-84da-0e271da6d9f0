const Jimp = require('jimp');

/**
 * Enhanced image validation and preprocessing utility for face analysis
 */
class ImageValidator {
  constructor() {
    // YouCam API requirements and recommendations
    this.requirements = {
      minWidth: 200,
      maxWidth: 2048,
      minHeight: 200, 
      maxHeight: 2048,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      minFileSize: 1024, // 1KB
      supportedFormats: ['image/jpeg', 'image/jpg', 'image/png'],
      minFaceSize: 50, // Minimum face size in pixels
      maxFaceSize: 1000 // Maximum face size in pixels
    };
  }

  /**
   * Validate base64 image data
   */
  async validateBase64Image(base64Data) {
    const validation = {
      isValid: false,
      errors: [],
      warnings: [],
      imageInfo: null,
      processedImage: null
    };

    try {
      // Step 1: Basic base64 validation
      if (!base64Data || typeof base64Data !== 'string') {
        validation.errors.push('Invalid base64 data provided');
        return validation;
      }

      // Remove data URL prefix if present
      let cleanBase64 = base64Data;
      if (base64Data.includes(',')) {
        const parts = base64Data.split(',');
        if (parts.length !== 2) {
          validation.errors.push('Invalid data URL format');
          return validation;
        }
        cleanBase64 = parts[1];
      }

      // Validate base64 format
      const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
      if (!base64Pattern.test(cleanBase64)) {
        validation.errors.push('Invalid base64 encoding format');
        return validation;
      }

      // Step 2: Size validation
      const buffer = Buffer.from(cleanBase64, 'base64');
      const fileSizeKB = (buffer.length / 1024).toFixed(2);
      
      if (buffer.length < this.requirements.minFileSize) {
        validation.errors.push(`Image too small (${fileSizeKB}KB). Minimum size: ${this.requirements.minFileSize / 1024}KB`);
      }
      
      if (buffer.length > this.requirements.maxFileSize) {
        validation.errors.push(`Image too large (${fileSizeKB}KB). Maximum size: ${this.requirements.maxFileSize / 1024 / 1024}MB`);
      }

      // Step 3: Load and analyze image with Jimp
      let image;
      try {
        image = await Jimp.read(buffer);
      } catch (jimpError) {
        validation.errors.push(`Invalid image format: ${jimpError.message}`);
        return validation;
      }

      // Step 4: Image dimension validation
      const { width, height } = image.bitmap;
      validation.imageInfo = {
        width,
        height,
        fileSizeKB: parseFloat(fileSizeKB),
        mimeType: image.getMIME(),
        aspectRatio: (width / height).toFixed(2)
      };

      if (width < this.requirements.minWidth || height < this.requirements.minHeight) {
        validation.errors.push(`Image dimensions too small (${width}x${height}). Minimum: ${this.requirements.minWidth}x${this.requirements.minHeight}`);
      }

      if (width > this.requirements.maxWidth || height > this.requirements.maxHeight) {
        validation.warnings.push(`Large image dimensions (${width}x${height}). Consider resizing for better performance.`);
      }

      // Step 5: Format validation
      const mimeType = image.getMIME();
      if (!this.requirements.supportedFormats.includes(mimeType)) {
        validation.errors.push(`Unsupported format: ${mimeType}. Supported: ${this.requirements.supportedFormats.join(', ')}`);
      }

      // Step 6: Image quality checks
      const qualityChecks = this.performQualityChecks(image);
      validation.warnings.push(...qualityChecks.warnings);
      validation.errors.push(...qualityChecks.errors);

      // Step 7: Preprocessing if needed
      if (validation.errors.length === 0) {
        validation.processedImage = await this.preprocessImage(image);
        validation.isValid = true;
      }

      return validation;

    } catch (error) {
      validation.errors.push(`Image validation failed: ${error.message}`);
      return validation;
    }
  }

  /**
   * Perform image quality checks
   */
  performQualityChecks(image) {
    const checks = { warnings: [], errors: [] };
    const { width, height } = image.bitmap;

    // Check aspect ratio (faces work best with certain ratios)
    const aspectRatio = width / height;
    if (aspectRatio < 0.5 || aspectRatio > 2.0) {
      checks.warnings.push(`Unusual aspect ratio (${aspectRatio.toFixed(2)}). Face detection works best with ratios between 0.5-2.0`);
    }

    // Check if image is too small for reliable face detection
    const minDimension = Math.min(width, height);
    if (minDimension < 300) {
      checks.warnings.push(`Small image dimensions may affect face detection accuracy. Consider using images with minimum 300px on the smaller side.`);
    }

    // Check for extremely large images that might timeout
    if (width * height > 4000000) { // 4MP
      checks.warnings.push(`Very large image (${width}x${height}). Consider resizing to improve processing speed.`);
    }

    return checks;
  }

  /**
   * Preprocess image for optimal face analysis
   */
  async preprocessImage(image) {
    try {
      let processedImage = image.clone();
      const { width, height } = processedImage.bitmap;

      // Resize if too large (maintain aspect ratio)
      const maxDimension = Math.max(width, height);
      if (maxDimension > 1024) {
        const scale = 1024 / maxDimension;
        const newWidth = Math.round(width * scale);
        const newHeight = Math.round(height * scale);
        processedImage = processedImage.resize(newWidth, newHeight, Jimp.RESIZE_LANCZOS);
        console.log(`Resized image from ${width}x${height} to ${newWidth}x${newHeight}`);
      }

      // Detect if image has skin conditions and apply specialized processing
      const hasSkinCondition = this.detectSkinCondition(processedImage);

      if (hasSkinCondition) {
        console.log('Skin condition detected - applying specialized preprocessing');
        processedImage = await this.preprocessSkinConditionImage(processedImage);
      } else {
        // Standard enhancement for normal images
        processedImage = processedImage
          .normalize() // Normalize colors
          .contrast(0.1) // Slight contrast enhancement
          .brightness(0.05); // Slight brightness enhancement
      }

      // Convert to JPEG with high quality if not already
      if (processedImage.getMIME() !== 'image/jpeg') {
        processedImage = processedImage.quality(95);
      }

      // Convert back to base64
      const buffer = await processedImage.getBufferAsync(Jimp.MIME_JPEG);
      return buffer.toString('base64');

    } catch (error) {
      console.error('Image preprocessing failed:', error);
      // Return original image as base64 if preprocessing fails
      const buffer = await image.getBufferAsync(image.getMIME());
      return buffer.toString('base64');
    }
  }

  /**
   * Detect if image has skin conditions (redness, rashes, etc.)
   */
  detectSkinCondition(image) {
    try {
      const { width, height, data } = image.bitmap;
      let redPixelCount = 0;
      let totalPixels = 0;
      let highSaturationCount = 0;

      // Sample pixels to detect unusual redness/coloration
      for (let y = 0; y < height; y += 5) { // Sample every 5th row for performance
        for (let x = 0; x < width; x += 5) { // Sample every 5th column
          const idx = (width * y + x) << 2;
          const r = data[idx];
          const g = data[idx + 1];
          const b = data[idx + 2];

          totalPixels++;

          // Detect red-dominant pixels (potential skin condition)
          if (r > g + 30 && r > b + 30 && r > 120) {
            redPixelCount++;
          }

          // Detect high saturation (unusual coloration)
          const max = Math.max(r, g, b);
          const min = Math.min(r, g, b);
          const saturation = max > 0 ? (max - min) / max : 0;

          if (saturation > 0.4 && max > 100) {
            highSaturationCount++;
          }
        }
      }

      const redPercentage = (redPixelCount / totalPixels) * 100;
      const saturationPercentage = (highSaturationCount / totalPixels) * 100;

      console.log(`Skin condition detection: ${redPercentage.toFixed(1)}% red pixels, ${saturationPercentage.toFixed(1)}% high saturation`);

      // Consider it a skin condition if >15% red pixels or >20% high saturation
      return redPercentage > 15 || saturationPercentage > 20;

    } catch (error) {
      console.error('Skin condition detection failed:', error);
      return false;
    }
  }

  /**
   * Specialized preprocessing for images with skin conditions
   */
  async preprocessSkinConditionImage(image) {
    try {
      let processedImage = image.clone();

      // Step 1: Reduce red channel intensity to normalize skin tone
      processedImage.scan(0, 0, processedImage.bitmap.width, processedImage.bitmap.height, function (x, y, idx) {
        const r = this.bitmap.data[idx + 0];
        const g = this.bitmap.data[idx + 1];
        const b = this.bitmap.data[idx + 2];

        // If red is dominant, reduce it and boost green/blue slightly
        if (r > g + 20 && r > b + 20) {
          this.bitmap.data[idx + 0] = Math.max(0, r * 0.7); // Reduce red by 30%
          this.bitmap.data[idx + 1] = Math.min(255, g * 1.1); // Boost green slightly
          this.bitmap.data[idx + 2] = Math.min(255, b * 1.1); // Boost blue slightly
        }
      });

      // Step 2: Apply gentle smoothing to reduce texture noise
      processedImage = processedImage.blur(1);

      // Step 3: Enhance contrast for better feature detection
      processedImage = processedImage
        .normalize() // Normalize color distribution
        .contrast(0.2) // Increase contrast more aggressively
        .brightness(0.1); // Slight brightness boost

      // Step 4: Convert to grayscale and back to reduce color interference
      const grayscale = processedImage.clone().greyscale();

      // Blend original with grayscale (70% original, 30% grayscale)
      processedImage.composite(grayscale, 0, 0, {
        mode: Jimp.BLEND_MULTIPLY,
        opacitySource: 0.3,
        opacityDest: 0.7
      });

      console.log('Applied specialized skin condition preprocessing');
      return processedImage;

    } catch (error) {
      console.error('Skin condition preprocessing failed:', error);
      return image; // Return original if processing fails
    }
  }

  /**
   * Generate user-friendly error message
   */
  generateErrorMessage(validation) {
    if (validation.isValid) return null;

    let message = 'Image validation failed:\n';
    validation.errors.forEach(error => {
      message += `• ${error}\n`;
    });

    if (validation.warnings.length > 0) {
      message += '\nWarnings:\n';
      validation.warnings.forEach(warning => {
        message += `• ${warning}\n`;
      });
    }

    message += '\nFor best results, please provide:\n';
    message += '• A clear, well-lit photo of a face\n';
    message += '• JPEG or PNG format\n';
    message += '• Minimum 300x300 pixels\n';
    message += '• File size between 1KB and 10MB\n';
    message += '• Face should be clearly visible and not too small or angled';

    return message;
  }
}

module.exports = ImageValidator;
