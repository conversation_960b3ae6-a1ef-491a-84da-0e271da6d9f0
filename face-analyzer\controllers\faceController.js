const { uploadImage, startAnalysis, getAnalysisResult } = require('../services/youcamService');
const ImageValidator = require('../utils/imageValidator');

// Upload image endpoint - Step 1 of the face analysis process
exports.uploadImage = async (req, res) => {
  try {
    console.log('=== UPLOAD IMAGE ENDPOINT ===');

    const imageValidator = new ImageValidator();

    // Check if image is provided
    const img = req.body.image;
    if (!img) {
      return res.status(400).json({
        error: 'No image provided',
        message: 'Please provide a base64 encoded image in the request body'
      });
    }

    console.log('Validating and preprocessing image...');

    // Enhanced validation and preprocessing
    const validation = await imageValidator.validateBase64Image(img);

    if (!validation.isValid) {
      console.log('Image validation failed:', validation.errors);
      return res.status(400).json({
        error: 'Image validation failed',
        details: validation.errors,
        warnings: validation.warnings,
        message: imageValidator.generateErrorMessage(validation),
        imageInfo: validation.imageInfo
      });
    }

    // Log validation results
    if (validation.warnings.length > 0) {
      console.log('Image validation warnings:', validation.warnings);
    }

    console.log('Image validation successful:', validation.imageInfo);
    console.log('Using processed image for upload...');

    // Use the processed image (optimized for face analysis)
    const processedImage = validation.processedImage;
    const fileId = await uploadImage(processedImage);
    console.log('✓ Upload successful, fileId:', fileId);

    res.json({
      success: true,
      fileId,
      message: 'Image uploaded and preprocessed successfully. Use this fileId to start analysis.',
      imageInfo: validation.imageInfo,
      warnings: validation.warnings,
      preprocessing: 'Image was optimized for better face detection',
      nextStep: 'POST /api/face/start-analysis with { "fileId": "' + fileId + '" }'
    });

  } catch (error) {
    console.error('Error in uploadImage:', error);
    res.status(500).json({
      error: error.message,
      message: 'Failed to process and upload image. Please check the image format and try again.'
    });
  }
};

// Start analysis endpoint - Step 2 of the face analysis process
exports.startAnalysis = async (req, res) => {
  try {
    console.log('=== START ANALYSIS ENDPOINT ===');

    const { fileId } = req.body;
    if (!fileId) {
      return res.status(400).json({ error: 'fileId is required' });
    }

    console.log('Starting analysis for fileId:', fileId);

    const taskId = await startAnalysis(fileId);
    console.log('✓ Analysis started, taskId:', taskId);

    res.json({
      success: true,
      taskId,
      message: 'Analysis started successfully. Use this taskId to get results.',
      nextStep: 'GET /api/face/result/' + taskId + ' (poll this endpoint until analysis is complete)'
    });

  } catch (error) {
    console.error('Error in startAnalysis:', error);
    res.status(500).json({ error: error.message });
  }
};

// Get analysis result endpoint - Step 3 of the face analysis process
exports.getAnalysisResult = async (req, res) => {
  try {
    console.log('=== GET ANALYSIS RESULT ENDPOINT ===');

    const { taskId } = req.params;
    if (!taskId) {
      return res.status(400).json({ error: 'taskId is required' });
    }

    console.log('Getting result for taskId:', taskId);
    console.log('TaskId length:', taskId.length);
    console.log('TaskId encoded:', encodeURIComponent(taskId));

    // Try to get the analysis result (this will poll internally)
    const result = await getAnalysisResult(taskId);
    console.log('✓ Analysis completed successfully');

    res.json({
      success: true,
      result,
      message: 'Analysis completed successfully',
      taskId: taskId
    });

  } catch (error) {
    console.error('Error in getAnalysisResult:', error);

    // Provide more helpful error messages based on the specific error
    if (error.message.includes('Invalid Task Id') || error.message.includes('HTTP 400')) {
      res.status(400).json({
        error: 'Invalid Task ID',
        message: 'The task ID may have expired, be malformed, or not exist.',
        details: error.message,
        taskId: req.params.taskId,
        suggestions: [
          'Verify the task ID is correct and complete',
          'Check if the task ID has expired (tasks typically expire after some time)',
          'Try creating a new analysis task with /start-analysis endpoint'
        ]
      });
    } else if (error.message.includes('timeout') || error.message.includes('likely no face detected')) {
      res.status(422).json({
        error: 'Analysis timeout',
        message: 'Face analysis timeout - this usually means no clear face was detected in the image.',
        details: error.message,
        suggestions: [
          'Ensure the image contains a clear, well-lit face',
          'Try with a higher quality image',
          'Make sure the face is not too small or at an extreme angle'
        ]
      });
    } else if (error.message.includes('still running') || error.message.includes('processing')) {
      res.status(202).json({
        success: false,
        status: 'processing',
        message: 'Analysis is still in progress. Please try again in a few seconds.',
        details: error.message,
        taskId: taskId,
        retryAfter: 5
      });
    } else if (error.message.includes('Analysis failed')) {
      res.status(422).json({
        error: 'Analysis failed',
        message: 'The face analysis process failed.',
        details: error.message,
        taskId: taskId
      });
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message,
        taskId: taskId
      });
    }
  }
};

// Test endpoint to create a fresh task ID for testing
exports.createTestTask = async (req, res) => {
  try {
    console.log('=== CREATE TEST TASK ENDPOINT ===');

    // Create a simple test image (1x1 pixel red PNG in base64)
    const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

    console.log('Step 1: Uploading test image...');
    const fileId = await uploadImage(testImageBase64);
    console.log('✓ Upload successful, fileId:', fileId);

    console.log('Step 2: Starting analysis...');
    const taskId = await startAnalysis(fileId);
    console.log('✓ Analysis started, taskId:', taskId);

    res.json({
      success: true,
      message: 'Test task created successfully',
      fileId,
      taskId,
      testEndpoint: `/api/face/result/${taskId}`,
      note: 'You can now test the result endpoint with this fresh taskId. Note: This test image likely won\'t produce meaningful face analysis results.'
    });

  } catch (error) {
    console.error('Error creating test task:', error);
    res.status(500).json({ error: error.message });
  }
};

// Enhanced debug endpoint to test the complete flow with detailed logging
exports.debugCompleteFlow = async (req, res) => {
  try {
    console.log('=== DEBUG COMPLETE FLOW ENDPOINT ===');

    const imageValidator = new ImageValidator();
    const img = req.body.image;

    if (!img) {
      return res.status(400).json({
        error: 'No image provided',
        message: 'Please provide a base64 encoded image in the request body'
      });
    }

    const debugInfo = {
      steps: [],
      timing: {},
      validation: null,
      errors: [],
      warnings: []
    };

    const startTime = Date.now();

    try {
      // Step 1: Validate and preprocess image
      console.log('Step 1: Validating and preprocessing image...');
      const stepStart = Date.now();

      const validation = await imageValidator.validateBase64Image(img);
      debugInfo.validation = validation;
      debugInfo.timing.validation = Date.now() - stepStart;

      if (!validation.isValid) {
        debugInfo.errors.push('Image validation failed');
        debugInfo.steps.push({
          step: 1,
          name: 'Image Validation',
          status: 'failed',
          errors: validation.errors,
          warnings: validation.warnings
        });

        return res.status(400).json({
          success: false,
          message: 'Image validation failed',
          debugInfo
        });
      }

      debugInfo.steps.push({
        step: 1,
        name: 'Image Validation & Preprocessing',
        status: 'success',
        duration: debugInfo.timing.validation,
        imageInfo: validation.imageInfo,
        warnings: validation.warnings
      });

      // Step 2: Upload image
      console.log('Step 2: Uploading processed image...');
      const uploadStart = Date.now();

      const fileId = await uploadImage(validation.processedImage);
      debugInfo.timing.upload = Date.now() - uploadStart;

      debugInfo.steps.push({
        step: 2,
        name: 'Image Upload',
        status: 'success',
        duration: debugInfo.timing.upload,
        fileId
      });

      // Step 3: Start analysis
      console.log('Step 3: Starting analysis...');
      const analysisStart = Date.now();

      const taskId = await startAnalysis(fileId);
      debugInfo.timing.analysisStart = Date.now() - analysisStart;

      debugInfo.steps.push({
        step: 3,
        name: 'Analysis Start',
        status: 'success',
        duration: debugInfo.timing.analysisStart,
        taskId
      });

      // Step 4: Get results (with enhanced polling)
      console.log('Step 4: Getting analysis results with enhanced polling...');
      const resultStart = Date.now();

      try {
        const result = await getAnalysisResult(taskId);
        debugInfo.timing.results = Date.now() - resultStart;
        debugInfo.timing.total = Date.now() - startTime;

        debugInfo.steps.push({
          step: 4,
          name: 'Analysis Results',
          status: 'success',
          duration: debugInfo.timing.results,
          resultSummary: {
            hasResults: !!result,
            resultKeys: result ? Object.keys(result) : []
          }
        });

        res.json({
          success: true,
          message: 'Complete flow executed successfully',
          result,
          debugInfo,
          recommendations: generateRecommendations(debugInfo)
        });

      } catch (resultError) {
        debugInfo.timing.results = Date.now() - resultStart;
        debugInfo.timing.total = Date.now() - startTime;

        debugInfo.steps.push({
          step: 4,
          name: 'Analysis Results',
          status: 'failed',
          duration: debugInfo.timing.results,
          error: resultError.message
        });

        // Provide detailed analysis of the failure
        const failureAnalysis = analyzeFailure(resultError, debugInfo);

        res.status(422).json({
          success: false,
          message: 'Analysis failed but flow completed partially',
          error: resultError.message,
          debugInfo,
          failureAnalysis,
          recommendations: generateRecommendations(debugInfo, resultError)
        });
      }

    } catch (error) {
      debugInfo.timing.total = Date.now() - startTime;
      debugInfo.errors.push(error.message);

      res.status(500).json({
        success: false,
        message: 'Flow failed during execution',
        error: error.message,
        debugInfo
      });
    }

  } catch (error) {
    console.error('Error in debug complete flow:', error);
    res.status(500).json({ error: error.message });
  }
};

// Helper function to analyze failure reasons
function analyzeFailure(error, debugInfo) {
  const analysis = {
    likelyReason: 'unknown',
    confidence: 'low',
    suggestions: []
  };

  if (error.message.includes('timeout')) {
    analysis.likelyReason = 'face_detection_failed';
    analysis.confidence = 'high';
    analysis.suggestions = [
      'The image likely does not contain a clear, detectable face',
      'Try with a different image that has a clear, well-lit face',
      'Ensure the face takes up at least 20% of the image area',
      'Check that the face is not too angled or partially obscured'
    ];
  } else if (error.message.includes('Invalid Task Id')) {
    analysis.likelyReason = 'task_expired';
    analysis.confidence = 'high';
    analysis.suggestions = [
      'The task ID expired before results could be retrieved',
      'This is unusual for a fresh task - check API credentials',
      'Try the flow again with a new image'
    ];
  }

  // Analyze image characteristics
  if (debugInfo.validation && debugInfo.validation.imageInfo) {
    const { width, height, fileSizeKB } = debugInfo.validation.imageInfo;

    if (width < 300 || height < 300) {
      analysis.suggestions.push('Image is quite small - try a larger image (minimum 300x300px)');
    }

    if (fileSizeKB < 10) {
      analysis.suggestions.push('Image file size is very small - this might indicate low quality');
    }
  }

  return analysis;
}

// Helper function to generate recommendations
function generateRecommendations(debugInfo, error = null) {
  const recommendations = [];

  if (debugInfo.validation) {
    if (debugInfo.validation.warnings.length > 0) {
      recommendations.push('Address image quality warnings for better results');
    }

    if (debugInfo.validation.imageInfo) {
      const { width, height } = debugInfo.validation.imageInfo;
      if (width < 500 || height < 500) {
        recommendations.push('Use higher resolution images (500x500px or larger) for better face detection');
      }
    }
  }

  if (error && error.message.includes('timeout')) {
    recommendations.push('Try with a clearer image containing a single, well-lit face');
    recommendations.push('Ensure the face is the main subject and takes up a significant portion of the image');
  }

  if (debugInfo.timing.total > 60000) {
    recommendations.push('Consider using smaller images to reduce processing time');
  }

  return recommendations;
}

// Specialized endpoint to analyze why face detection is failing
exports.analyzeFaceDetectionIssues = async (req, res) => {
  try {
    console.log('=== FACE DETECTION ISSUE ANALYSIS ===');

    const imageValidator = new ImageValidator();
    const img = req.body.image;

    if (!img) {
      return res.status(400).json({
        error: 'No image provided',
        message: 'Please provide a base64 encoded image in the request body'
      });
    }

    // Perform detailed image analysis
    const validation = await imageValidator.validateBase64Image(img);

    // Analyze specific face detection issues
    const faceDetectionAnalysis = await analyzeFaceDetectionProblems(validation);

    res.json({
      success: true,
      message: 'Face detection analysis completed',
      imageValidation: {
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
        imageInfo: validation.imageInfo
      },
      faceDetectionAnalysis,
      recommendations: generateFaceDetectionRecommendations(faceDetectionAnalysis, validation),
      nextSteps: [
        'Address the identified issues',
        'Try with a frontal face image if using profile view',
        'Ensure good lighting and clear facial features',
        'Test with the debug-flow endpoint after making changes'
      ]
    });

  } catch (error) {
    console.error('Error in face detection analysis:', error);
    res.status(500).json({ error: error.message });
  }
};

// Analyze specific face detection problems
async function analyzeFaceDetectionProblems(validation) {
  const analysis = {
    likelyIssues: [],
    severity: 'unknown',
    confidence: 0,
    technicalDetails: {}
  };

  if (!validation.isValid) {
    analysis.likelyIssues.push({
      issue: 'image_validation_failed',
      severity: 'critical',
      description: 'Basic image validation failed',
      impact: 'Face detection cannot proceed'
    });
    analysis.severity = 'critical';
    analysis.confidence = 100;
    return analysis;
  }

  const { imageInfo } = validation;

  // Analyze image dimensions
  if (imageInfo.width < 300 || imageInfo.height < 300) {
    analysis.likelyIssues.push({
      issue: 'image_too_small',
      severity: 'high',
      description: `Image dimensions (${imageInfo.width}x${imageInfo.height}) are too small for reliable face detection`,
      impact: 'Face features may not be detectable',
      recommendation: 'Use images at least 300x300 pixels'
    });
  }

  // Analyze aspect ratio for profile detection
  const aspectRatio = parseFloat(imageInfo.aspectRatio);
  if (aspectRatio > 1.5 || aspectRatio < 0.67) {
    analysis.likelyIssues.push({
      issue: 'unusual_aspect_ratio',
      severity: 'medium',
      description: `Unusual aspect ratio (${aspectRatio}) may indicate cropped or profile image`,
      impact: 'May indicate partial face or profile view',
      recommendation: 'Use square or portrait orientation images with full face visible'
    });
  }

  // Analyze file size for quality estimation
  if (imageInfo.fileSizeKB < 20) {
    analysis.likelyIssues.push({
      issue: 'low_quality_suspected',
      severity: 'medium',
      description: `Small file size (${imageInfo.fileSizeKB}KB) may indicate low quality or compression`,
      impact: 'Facial features may be unclear due to compression artifacts',
      recommendation: 'Use higher quality images with less compression'
    });
  }

  // Determine overall severity
  const severities = analysis.likelyIssues.map(issue => issue.severity);
  if (severities.includes('critical')) {
    analysis.severity = 'critical';
    analysis.confidence = 95;
  } else if (severities.includes('high')) {
    analysis.severity = 'high';
    analysis.confidence = 80;
  } else if (severities.includes('medium')) {
    analysis.severity = 'medium';
    analysis.confidence = 60;
  } else {
    analysis.severity = 'low';
    analysis.confidence = 30;
    analysis.likelyIssues.push({
      issue: 'face_detection_algorithm_limitation',
      severity: 'low',
      description: 'Image appears technically valid but face detection still failing',
      impact: 'May be due to pose, lighting, or facial features not meeting API requirements',
      recommendation: 'Try with different lighting, frontal pose, or clearer facial features'
    });
  }

  analysis.technicalDetails = {
    imageFormat: imageInfo.mimeType,
    dimensions: `${imageInfo.width}x${imageInfo.height}`,
    fileSize: `${imageInfo.fileSizeKB}KB`,
    aspectRatio: imageInfo.aspectRatio
  };

  return analysis;
}

// Generate specific recommendations for face detection issues
function generateFaceDetectionRecommendations(analysis, validation) {
  const recommendations = [];

  // Critical issues first
  const criticalIssues = analysis.likelyIssues.filter(issue => issue.severity === 'critical');
  if (criticalIssues.length > 0) {
    recommendations.push('🚨 CRITICAL: Fix image validation errors first');
    criticalIssues.forEach(issue => {
      recommendations.push(`   • ${issue.recommendation}`);
    });
    return recommendations;
  }

  // High priority issues
  const highIssues = analysis.likelyIssues.filter(issue => issue.severity === 'high');
  if (highIssues.length > 0) {
    recommendations.push('⚠️ HIGH PRIORITY:');
    highIssues.forEach(issue => {
      recommendations.push(`   • ${issue.recommendation}`);
    });
  }

  // Medium priority issues
  const mediumIssues = analysis.likelyIssues.filter(issue => issue.severity === 'medium');
  if (mediumIssues.length > 0) {
    recommendations.push('📋 MEDIUM PRIORITY:');
    mediumIssues.forEach(issue => {
      recommendations.push(`   • ${issue.recommendation}`);
    });
  }

  // General recommendations based on common face detection requirements
  recommendations.push('');
  recommendations.push('📸 GENERAL FACE DETECTION BEST PRACTICES:');
  recommendations.push('   • Use frontal face view (not profile or angled)');
  recommendations.push('   • Ensure both eyes and mouth are clearly visible');
  recommendations.push('   • Use even lighting without harsh shadows');
  recommendations.push('   • Face should occupy 20-50% of the image area');
  recommendations.push('   • Avoid obstructions (hair, hands, objects)');
  recommendations.push('   • Use neutral expression when possible');
  recommendations.push('   • Ensure good contrast between face and background');

  // Specific recommendations for the user's image type
  if (analysis.likelyIssues.some(issue => issue.issue === 'unusual_aspect_ratio')) {
    recommendations.push('');
    recommendations.push('🎯 FOR YOUR SPECIFIC IMAGE:');
    recommendations.push('   • Your image appears to be a profile/side view');
    recommendations.push('   • Face analysis APIs require frontal face views');
    recommendations.push('   • Take a new photo facing directly toward the camera');
    recommendations.push('   • Ensure both eyes are visible in the image');
  }

  return recommendations;
}

// Specialized endpoint for testing skin condition images
exports.testSkinConditionImage = async (req, res) => {
  try {
    console.log('=== SKIN CONDITION IMAGE TEST ===');

    const imageValidator = new ImageValidator();
    const img = req.body.image;

    if (!img) {
      return res.status(400).json({
        error: 'No image provided',
        message: 'Please provide a base64 encoded image in the request body'
      });
    }

    const testResults = {
      originalValidation: null,
      skinConditionDetection: null,
      preprocessingResults: [],
      recommendations: []
    };

    // Step 1: Validate original image
    console.log('Step 1: Validating original image...');
    const validation = await imageValidator.validateBase64Image(img);
    testResults.originalValidation = {
      isValid: validation.isValid,
      errors: validation.errors,
      warnings: validation.warnings,
      imageInfo: validation.imageInfo
    };

    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Original image validation failed',
        testResults
      });
    }

    // Step 2: Test skin condition detection
    console.log('Step 2: Testing skin condition detection...');
    const Jimp = require('jimp');
    const buffer = Buffer.from(validation.processedImage, 'base64');
    const image = await Jimp.read(buffer);

    const hasSkinCondition = imageValidator.detectSkinCondition(image);
    testResults.skinConditionDetection = {
      detected: hasSkinCondition,
      message: hasSkinCondition ?
        'Skin condition detected - will apply specialized preprocessing' :
        'No skin condition detected - will use standard preprocessing'
    };

    // Step 3: Test different preprocessing approaches
    console.log('Step 3: Testing multiple preprocessing approaches...');

    // Approach 1: Standard preprocessing
    const standardProcessed = await imageValidator.preprocessImage(image);
    testResults.preprocessingResults.push({
      approach: 'standard',
      description: 'Standard image enhancement (contrast, brightness, normalization)',
      processed: true
    });

    // Approach 2: Skin condition preprocessing (if detected)
    if (hasSkinCondition) {
      const skinConditionProcessed = await imageValidator.preprocessSkinConditionImage(image);
      testResults.preprocessingResults.push({
        approach: 'skin_condition_specialized',
        description: 'Specialized preprocessing for skin conditions (red reduction, smoothing, enhanced contrast)',
        processed: true
      });
    }

    // Step 4: Generate recommendations
    testResults.recommendations = [
      'Image validation completed successfully',
      hasSkinCondition ?
        '⚠️ Skin condition detected - using specialized preprocessing' :
        '✅ Normal skin detected - using standard preprocessing',
      '',
      '🧪 TESTING RECOMMENDATIONS:',
      '1. Try the processed image with the debug-flow endpoint',
      '2. If still failing, the YouCam API may not support this type of skin condition',
      '3. Consider using a different face analysis API that handles medical conditions',
      '4. Alternative: Test with a person without visible skin conditions first',
      '',
      '🎯 FOR SKIN CONDITION DOCUMENTATION:',
      '• Some face analysis APIs are not designed for medical/dermatological images',
      '• Consider APIs specifically designed for medical imaging',
      '• Document the limitation in your application',
      '• Provide clear user guidance about supported image types'
    ];

    // Step 5: Attempt a quick test with YouCam API
    console.log('Step 4: Testing with YouCam API...');
    try {
      const fileId = await uploadImage(validation.processedImage);
      const taskId = await startAnalysis(fileId);

      testResults.apiTest = {
        uploadSuccess: true,
        analysisStarted: true,
        fileId,
        taskId,
        message: 'API test successful - image uploaded and analysis started',
        nextStep: `Test results with: GET /api/face/result/${taskId}`
      };

    } catch (apiError) {
      testResults.apiTest = {
        uploadSuccess: false,
        error: apiError.message,
        message: 'API test failed during upload or analysis start'
      };
    }

    res.json({
      success: true,
      message: 'Skin condition image test completed',
      testResults,
      nextSteps: [
        'Review the skin condition detection results',
        'Try the processed image with different endpoints',
        'Consider alternative approaches if YouCam API continues to fail',
        'Document API limitations for skin condition images'
      ]
    });

  } catch (error) {
    console.error('Error in skin condition image test:', error);
    res.status(500).json({ error: error.message });
  }
};

// Legacy combined endpoint - performs all three steps in one call
// DEPRECATED: Use the separate endpoints above for better control
exports.analyzeFace = async (req, res) => {
  try {
    console.log('=== LEGACY COMBINED ANALYSIS ENDPOINT ===');
    console.log('⚠️  DEPRECATED: Consider using separate endpoints for better control');

    // Handle both data URL format and direct base64
    let img = req.body.image;
    if (!img) {
      return res.status(400).json({ error: 'No image provided' });
    }

    // If it's a data URL, extract the base64 part
    if (img.includes(',')) {
      const parts = img.split(',');
      if (parts.length !== 2) {
        return res.status(400).json({ error: 'Invalid data URL format' });
      }
      img = parts[1];
    }

    // Validate base64 format
    if (!img || img.length < 1000) {
      return res.status(400).json({
        error: 'Image too small - please provide a larger image with a clear face (minimum ~1KB)'
      });
    }

    // Basic base64 validation
    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
    if (!base64Pattern.test(img)) {
      return res.status(400).json({ error: 'Invalid base64 format' });
    }

    console.log('Processing image of length:', img.length);

    try {
      // Step 1: Upload image
      console.log('Step 1: Uploading image...');
      const fileId = await uploadImage(img);
      console.log('✓ Upload successful, fileId:', fileId);

      // Step 2: Start analysis
      console.log('Step 2: Starting analysis...');
      const taskId = await startAnalysis(fileId);
      console.log('✓ Analysis started, taskId:', taskId);

      // Step 3: Get results
      console.log('Step 3: Getting analysis results...');
      const result = await getAnalysisResult(taskId);
      console.log('✓ Analysis completed');

      res.json({
        success: true,
        result,
        fileId,
        taskId,
        message: 'Complete analysis finished successfully',
        recommendation: 'Consider using separate endpoints (/upload, /start-analysis, /result/:taskId) for better control and error handling'
      });

    } catch (analysisError) {
      // Provide more helpful error messages
      if (analysisError.message.includes('timeout')) {
        res.status(422).json({
          error: 'Face analysis timeout - this usually means no clear face was detected in the image. Please ensure the image contains a clear, well-lit face.',
          details: analysisError.message
        });
      } else {
        throw analysisError; // Re-throw other errors
      }
    }
  } catch (error) {
    console.error('Error in analyzeFace:', error);
    res.status(500).json({ error: error.message });
  }
};
