const { uploadImage, startAnalysis, getAnalysisResult } = require('../services/youcamService');

// Upload image endpoint - Step 1 of the face analysis process
exports.uploadImage = async (req, res) => {
  try {
    console.log('=== UPLOAD IMAGE ENDPOINT ===');

    // Handle both data URL format and direct base64
    let img = req.body.image;
    if (!img) {
      return res.status(400).json({ error: 'No image provided' });
    }

    // If it's a data URL, extract the base64 part
    if (img.includes(',')) {
      const parts = img.split(',');
      if (parts.length !== 2) {
        return res.status(400).json({ error: 'Invalid data URL format' });
      }
      img = parts[1];
    }

    // Validate base64 format
    if (!img || img.length < 1000) {
      return res.status(400).json({
        error: 'Image too small - please provide a larger image with a clear face (minimum ~1KB)'
      });
    }

    // Basic base64 validation
    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
    if (!base64Pattern.test(img)) {
      return res.status(400).json({ error: 'Invalid base64 format' });
    }

    console.log('Processing image of length:', img.length);
    console.log('Image starts with:', img.substring(0, 50));

    const fileId = await uploadImage(img);
    console.log('✓ Upload successful, fileId:', fileId);

    res.json({
      success: true,
      fileId,
      message: 'Image uploaded successfully. Use this fileId to start analysis.',
      nextStep: 'POST /api/face/start-analysis with { "fileId": "' + fileId + '" }'
    });

  } catch (error) {
    console.error('Error in uploadImage:', error);
    res.status(500).json({ error: error.message });
  }
};

// Start analysis endpoint - Step 2 of the face analysis process
exports.startAnalysis = async (req, res) => {
  try {
    console.log('=== START ANALYSIS ENDPOINT ===');

    const { fileId } = req.body;
    if (!fileId) {
      return res.status(400).json({ error: 'fileId is required' });
    }

    console.log('Starting analysis for fileId:', fileId);

    const taskId = await startAnalysis(fileId);
    console.log('✓ Analysis started, taskId:', taskId);

    res.json({
      success: true,
      taskId,
      message: 'Analysis started successfully. Use this taskId to get results.',
      nextStep: 'GET /api/face/result/' + taskId + ' (poll this endpoint until analysis is complete)'
    });

  } catch (error) {
    console.error('Error in startAnalysis:', error);
    res.status(500).json({ error: error.message });
  }
};

// Get analysis result endpoint - Step 3 of the face analysis process
exports.getAnalysisResult = async (req, res) => {
  try {
    console.log('=== GET ANALYSIS RESULT ENDPOINT ===');

    const { taskId } = req.params;
    if (!taskId) {
      return res.status(400).json({ error: 'taskId is required' });
    }

    console.log('Getting result for taskId:', taskId);

    const result = await getAnalysisResult(taskId);
    console.log('✓ Analysis completed');

    res.json({
      success: true,
      result,
      message: 'Analysis completed successfully'
    });

  } catch (error) {
    console.error('Error in getAnalysisResult:', error);

    // Provide more helpful error messages
    if (error.message.includes('timeout')) {
      res.status(422).json({
        error: 'Face analysis timeout - this usually means no clear face was detected in the image. Please ensure the image contains a clear, well-lit face.',
        details: error.message
      });
    } else if (error.message.includes('still running') || error.message.includes('processing')) {
      res.status(202).json({
        success: false,
        status: 'processing',
        message: 'Analysis is still in progress. Please try again in a few seconds.',
        details: error.message
      });
    } else {
      res.status(500).json({ error: error.message });
    }
  }
};

// Legacy combined endpoint - performs all three steps in one call
// DEPRECATED: Use the separate endpoints above for better control
// exports.analyzeFace = async (req, res) => {
//   try {
//     console.log('=== LEGACY COMBINED ANALYSIS ENDPOINT ===');
//     console.log('⚠️  DEPRECATED: Consider using separate endpoints for better control');

//     // Handle both data URL format and direct base64
//     let img = req.body.image;
//     if (!img) {
//       return res.status(400).json({ error: 'No image provided' });
//     }

//     // If it's a data URL, extract the base64 part
//     if (img.includes(',')) {
//       const parts = img.split(',');
//       if (parts.length !== 2) {
//         return res.status(400).json({ error: 'Invalid data URL format' });
//       }
//       img = parts[1];
//     }

//     // Validate base64 format
//     if (!img || img.length < 1000) {
//       return res.status(400).json({
//         error: 'Image too small - please provide a larger image with a clear face (minimum ~1KB)'
//       });
//     }

//     // Basic base64 validation
//     const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
//     if (!base64Pattern.test(img)) {
//       return res.status(400).json({ error: 'Invalid base64 format' });
//     }

//     console.log('Processing image of length:', img.length);

//     try {
//       // Step 1: Upload image
//       console.log('Step 1: Uploading image...');
//       const fileId = await uploadImage(img);
//       console.log('✓ Upload successful, fileId:', fileId);

//       // Step 2: Start analysis
//       console.log('Step 2: Starting analysis...');
//       const taskId = await startAnalysis(fileId);
//       console.log('✓ Analysis started, taskId:', taskId);

//       // Step 3: Get results
//       console.log('Step 3: Getting analysis results...');
//       const result = await getAnalysisResult(taskId);
//       console.log('✓ Analysis completed');

//       res.json({
//         success: true,
//         result,
//         fileId,
//         taskId,
//         message: 'Complete analysis finished successfully',
//         recommendation: 'Consider using separate endpoints (/upload, /start-analysis, /result/:taskId) for better control and error handling'
//       });

//     } catch (analysisError) {
//       // Provide more helpful error messages
//       if (analysisError.message.includes('timeout')) {
//         res.status(422).json({
//           error: 'Face analysis timeout - this usually means no clear face was detected in the image. Please ensure the image contains a clear, well-lit face.',
//           details: analysisError.message
//         });
//       } else {
//         throw analysisError; // Re-throw other errors
//       }
//     }
//   } catch (error) {
//     console.error('Error in analyzeFace:', error);
//     res.status(500).json({ error: error.message });
//   }
// };
