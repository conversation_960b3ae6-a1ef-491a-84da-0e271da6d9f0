const { uploadImage, startAnalysis, getAnalysisResult } = require('../services/youcamService');
const ImageValidator = require('../utils/imageValidator');

// Upload image endpoint - Step 1 of the face analysis process
exports.uploadImage = async (req, res) => {
  try {
    console.log('=== UPLOAD IMAGE ENDPOINT ===');

    const imageValidator = new ImageValidator();

    // Check if image is provided
    const img = req.body.image;
    if (!img) {
      return res.status(400).json({
        error: 'No image provided',
        message: 'Please provide a base64 encoded image in the request body'
      });
    }

    console.log('Validating and preprocessing image...');

    // Enhanced validation and preprocessing
    const validation = await imageValidator.validateBase64Image(img);

    if (!validation.isValid) {
      console.log('Image validation failed:', validation.errors);
      return res.status(400).json({
        error: 'Image validation failed',
        details: validation.errors,
        warnings: validation.warnings,
        message: imageValidator.generateErrorMessage(validation),
        imageInfo: validation.imageInfo
      });
    }

    // Log validation results
    if (validation.warnings.length > 0) {
      console.log('Image validation warnings:', validation.warnings);
    }

    console.log('Image validation successful:', validation.imageInfo);
    console.log('Using processed image for upload...');

    // Use the processed image (optimized for face analysis)
    const processedImage = validation.processedImage;
    const fileId = await uploadImage(processedImage);
    console.log('✓ Upload successful, fileId:', fileId);

    res.json({
      success: true,
      fileId,
      message: 'Image uploaded and preprocessed successfully. Use this fileId to start analysis.',
      imageInfo: validation.imageInfo,
      warnings: validation.warnings,
      preprocessing: 'Image was optimized for better face detection',
      nextStep: 'POST /api/face/start-analysis with { "fileId": "' + fileId + '" }'
    });

  } catch (error) {
    console.error('Error in uploadImage:', error);
    res.status(500).json({
      error: error.message,
      message: 'Failed to process and upload image. Please check the image format and try again.'
    });
  }
};

// Start analysis endpoint - Step 2 of the face analysis process
exports.startAnalysis = async (req, res) => {
  try {
    console.log('=== START ANALYSIS ENDPOINT ===');

    const { fileId } = req.body;
    if (!fileId) {
      return res.status(400).json({ error: 'fileId is required' });
    }

    console.log('Starting analysis for fileId:', fileId);

    const taskId = await startAnalysis(fileId);
    console.log('✓ Analysis started, taskId:', taskId);

    res.json({
      success: true,
      taskId,
      message: 'Analysis started successfully. Use this taskId to get results.',
      nextStep: 'GET /api/face/result/' + taskId + ' (poll this endpoint until analysis is complete)'
    });

  } catch (error) {
    console.error('Error in startAnalysis:', error);
    res.status(500).json({ error: error.message });
  }
};

// Get analysis result endpoint - Step 3 of the face analysis process
exports.getAnalysisResult = async (req, res) => {
  try {
    console.log('=== GET ANALYSIS RESULT ENDPOINT ===');

    const { taskId } = req.params;
    if (!taskId) {
      return res.status(400).json({ error: 'taskId is required' });
    }

    console.log('Getting result for taskId:', taskId);
    console.log('TaskId length:', taskId.length);
    console.log('TaskId encoded:', encodeURIComponent(taskId));

    // Try to get the analysis result (this will poll internally)
    const result = await getAnalysisResult(taskId);
    console.log('✓ Analysis completed successfully');

    res.json({
      success: true,
      result,
      message: 'Analysis completed successfully',
      taskId: taskId
    });

  } catch (error) {
    console.error('Error in getAnalysisResult:', error);

    // Provide more helpful error messages based on the specific error
    if (error.message.includes('Invalid Task Id') || error.message.includes('HTTP 400')) {
      res.status(400).json({
        error: 'Invalid Task ID',
        message: 'The task ID may have expired, be malformed, or not exist.',
        details: error.message,
        taskId: req.params.taskId,
        suggestions: [
          'Verify the task ID is correct and complete',
          'Check if the task ID has expired (tasks typically expire after some time)',
          'Try creating a new analysis task with /start-analysis endpoint'
        ]
      });
    } else if (error.message.includes('timeout') || error.message.includes('likely no face detected')) {
      res.status(422).json({
        error: 'Analysis timeout',
        message: 'Face analysis timeout - this usually means no clear face was detected in the image.',
        details: error.message,
        suggestions: [
          'Ensure the image contains a clear, well-lit face',
          'Try with a higher quality image',
          'Make sure the face is not too small or at an extreme angle'
        ]
      });
    } else if (error.message.includes('still running') || error.message.includes('processing')) {
      res.status(202).json({
        success: false,
        status: 'processing',
        message: 'Analysis is still in progress. Please try again in a few seconds.',
        details: error.message,
        taskId: taskId,
        retryAfter: 5
      });
    } else if (error.message.includes('Analysis failed')) {
      res.status(422).json({
        error: 'Analysis failed',
        message: 'The face analysis process failed.',
        details: error.message,
        taskId: taskId
      });
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message,
        taskId: taskId
      });
    }
  }
};

// Test endpoint to create a fresh task ID for testing
exports.createTestTask = async (req, res) => {
  try {
    console.log('=== CREATE TEST TASK ENDPOINT ===');

    // Create a simple test image (1x1 pixel red PNG in base64)
    const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

    console.log('Step 1: Uploading test image...');
    const fileId = await uploadImage(testImageBase64);
    console.log('✓ Upload successful, fileId:', fileId);

    console.log('Step 2: Starting analysis...');
    const taskId = await startAnalysis(fileId);
    console.log('✓ Analysis started, taskId:', taskId);

    res.json({
      success: true,
      message: 'Test task created successfully',
      fileId,
      taskId,
      testEndpoint: `/api/face/result/${taskId}`,
      note: 'You can now test the result endpoint with this fresh taskId. Note: This test image likely won\'t produce meaningful face analysis results.'
    });

  } catch (error) {
    console.error('Error creating test task:', error);
    res.status(500).json({ error: error.message });
  }
};

// Enhanced debug endpoint to test the complete flow with detailed logging
exports.debugCompleteFlow = async (req, res) => {
  try {
    console.log('=== DEBUG COMPLETE FLOW ENDPOINT ===');

    const imageValidator = new ImageValidator();
    const img = req.body.image;

    if (!img) {
      return res.status(400).json({
        error: 'No image provided',
        message: 'Please provide a base64 encoded image in the request body'
      });
    }

    const debugInfo = {
      steps: [],
      timing: {},
      validation: null,
      errors: [],
      warnings: []
    };

    const startTime = Date.now();

    try {
      // Step 1: Validate and preprocess image
      console.log('Step 1: Validating and preprocessing image...');
      const stepStart = Date.now();

      const validation = await imageValidator.validateBase64Image(img);
      debugInfo.validation = validation;
      debugInfo.timing.validation = Date.now() - stepStart;

      if (!validation.isValid) {
        debugInfo.errors.push('Image validation failed');
        debugInfo.steps.push({
          step: 1,
          name: 'Image Validation',
          status: 'failed',
          errors: validation.errors,
          warnings: validation.warnings
        });

        return res.status(400).json({
          success: false,
          message: 'Image validation failed',
          debugInfo
        });
      }

      debugInfo.steps.push({
        step: 1,
        name: 'Image Validation & Preprocessing',
        status: 'success',
        duration: debugInfo.timing.validation,
        imageInfo: validation.imageInfo,
        warnings: validation.warnings
      });

      // Step 2: Upload image
      console.log('Step 2: Uploading processed image...');
      const uploadStart = Date.now();

      const fileId = await uploadImage(validation.processedImage);
      debugInfo.timing.upload = Date.now() - uploadStart;

      debugInfo.steps.push({
        step: 2,
        name: 'Image Upload',
        status: 'success',
        duration: debugInfo.timing.upload,
        fileId
      });

      // Step 3: Start analysis
      console.log('Step 3: Starting analysis...');
      const analysisStart = Date.now();

      const taskId = await startAnalysis(fileId);
      debugInfo.timing.analysisStart = Date.now() - analysisStart;

      debugInfo.steps.push({
        step: 3,
        name: 'Analysis Start',
        status: 'success',
        duration: debugInfo.timing.analysisStart,
        taskId
      });

      // Step 4: Get results (with enhanced polling)
      console.log('Step 4: Getting analysis results with enhanced polling...');
      const resultStart = Date.now();

      try {
        const result = await getAnalysisResult(taskId);
        debugInfo.timing.results = Date.now() - resultStart;
        debugInfo.timing.total = Date.now() - startTime;

        debugInfo.steps.push({
          step: 4,
          name: 'Analysis Results',
          status: 'success',
          duration: debugInfo.timing.results,
          resultSummary: {
            hasResults: !!result,
            resultKeys: result ? Object.keys(result) : []
          }
        });

        res.json({
          success: true,
          message: 'Complete flow executed successfully',
          result,
          debugInfo,
          recommendations: generateRecommendations(debugInfo)
        });

      } catch (resultError) {
        debugInfo.timing.results = Date.now() - resultStart;
        debugInfo.timing.total = Date.now() - startTime;

        debugInfo.steps.push({
          step: 4,
          name: 'Analysis Results',
          status: 'failed',
          duration: debugInfo.timing.results,
          error: resultError.message
        });

        // Provide detailed analysis of the failure
        const failureAnalysis = analyzeFailure(resultError, debugInfo);

        res.status(422).json({
          success: false,
          message: 'Analysis failed but flow completed partially',
          error: resultError.message,
          debugInfo,
          failureAnalysis,
          recommendations: generateRecommendations(debugInfo, resultError)
        });
      }

    } catch (error) {
      debugInfo.timing.total = Date.now() - startTime;
      debugInfo.errors.push(error.message);

      res.status(500).json({
        success: false,
        message: 'Flow failed during execution',
        error: error.message,
        debugInfo
      });
    }

  } catch (error) {
    console.error('Error in debug complete flow:', error);
    res.status(500).json({ error: error.message });
  }
};

// Helper function to analyze failure reasons
function analyzeFailure(error, debugInfo) {
  const analysis = {
    likelyReason: 'unknown',
    confidence: 'low',
    suggestions: []
  };

  if (error.message.includes('timeout')) {
    analysis.likelyReason = 'face_detection_failed';
    analysis.confidence = 'high';
    analysis.suggestions = [
      'The image likely does not contain a clear, detectable face',
      'Try with a different image that has a clear, well-lit face',
      'Ensure the face takes up at least 20% of the image area',
      'Check that the face is not too angled or partially obscured'
    ];
  } else if (error.message.includes('Invalid Task Id')) {
    analysis.likelyReason = 'task_expired';
    analysis.confidence = 'high';
    analysis.suggestions = [
      'The task ID expired before results could be retrieved',
      'This is unusual for a fresh task - check API credentials',
      'Try the flow again with a new image'
    ];
  }

  // Analyze image characteristics
  if (debugInfo.validation && debugInfo.validation.imageInfo) {
    const { width, height, fileSizeKB } = debugInfo.validation.imageInfo;

    if (width < 300 || height < 300) {
      analysis.suggestions.push('Image is quite small - try a larger image (minimum 300x300px)');
    }

    if (fileSizeKB < 10) {
      analysis.suggestions.push('Image file size is very small - this might indicate low quality');
    }
  }

  return analysis;
}

// Helper function to generate recommendations
function generateRecommendations(debugInfo, error = null) {
  const recommendations = [];

  if (debugInfo.validation) {
    if (debugInfo.validation.warnings.length > 0) {
      recommendations.push('Address image quality warnings for better results');
    }

    if (debugInfo.validation.imageInfo) {
      const { width, height } = debugInfo.validation.imageInfo;
      if (width < 500 || height < 500) {
        recommendations.push('Use higher resolution images (500x500px or larger) for better face detection');
      }
    }
  }

  if (error && error.message.includes('timeout')) {
    recommendations.push('Try with a clearer image containing a single, well-lit face');
    recommendations.push('Ensure the face is the main subject and takes up a significant portion of the image');
  }

  if (debugInfo.timing.total > 60000) {
    recommendations.push('Consider using smaller images to reduce processing time');
  }

  return recommendations;
}

// Legacy combined endpoint - performs all three steps in one call
// DEPRECATED: Use the separate endpoints above for better control
exports.analyzeFace = async (req, res) => {
  try {
    console.log('=== LEGACY COMBINED ANALYSIS ENDPOINT ===');
    console.log('⚠️  DEPRECATED: Consider using separate endpoints for better control');

    // Handle both data URL format and direct base64
    let img = req.body.image;
    if (!img) {
      return res.status(400).json({ error: 'No image provided' });
    }

    // If it's a data URL, extract the base64 part
    if (img.includes(',')) {
      const parts = img.split(',');
      if (parts.length !== 2) {
        return res.status(400).json({ error: 'Invalid data URL format' });
      }
      img = parts[1];
    }

    // Validate base64 format
    if (!img || img.length < 1000) {
      return res.status(400).json({
        error: 'Image too small - please provide a larger image with a clear face (minimum ~1KB)'
      });
    }

    // Basic base64 validation
    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
    if (!base64Pattern.test(img)) {
      return res.status(400).json({ error: 'Invalid base64 format' });
    }

    console.log('Processing image of length:', img.length);

    try {
      // Step 1: Upload image
      console.log('Step 1: Uploading image...');
      const fileId = await uploadImage(img);
      console.log('✓ Upload successful, fileId:', fileId);

      // Step 2: Start analysis
      console.log('Step 2: Starting analysis...');
      const taskId = await startAnalysis(fileId);
      console.log('✓ Analysis started, taskId:', taskId);

      // Step 3: Get results
      console.log('Step 3: Getting analysis results...');
      const result = await getAnalysisResult(taskId);
      console.log('✓ Analysis completed');

      res.json({
        success: true,
        result,
        fileId,
        taskId,
        message: 'Complete analysis finished successfully',
        recommendation: 'Consider using separate endpoints (/upload, /start-analysis, /result/:taskId) for better control and error handling'
      });

    } catch (analysisError) {
      // Provide more helpful error messages
      if (analysisError.message.includes('timeout')) {
        res.status(422).json({
          error: 'Face analysis timeout - this usually means no clear face was detected in the image. Please ensure the image contains a clear, well-lit face.',
          details: analysisError.message
        });
      } else {
        throw analysisError; // Re-throw other errors
      }
    }
  } catch (error) {
    console.error('Error in analyzeFace:', error);
    res.status(500).json({ error: error.message });
  }
};
