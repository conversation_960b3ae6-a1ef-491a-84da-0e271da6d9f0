const { uploadImage, startAnalysis, getAnalysisResult } = require('../services/youcamService');

// Upload image endpoint - Step 1 of the face analysis process
exports.uploadImage = async (req, res) => {
  try {
    console.log('=== UPLOAD IMAGE ENDPOINT ===');

    // Handle both data URL format and direct base64
    let img = req.body.image;
    if (!img) {
      return res.status(400).json({ error: 'No image provided' });
    }

    // If it's a data URL, extract the base64 part
    if (img.includes(',')) {
      const parts = img.split(',');
      if (parts.length !== 2) {
        return res.status(400).json({ error: 'Invalid data URL format' });
      }
      img = parts[1];
    }

    // Validate base64 format
    if (!img || img.length < 1000) {
      return res.status(400).json({
        error: 'Image too small - please provide a larger image with a clear face (minimum ~1KB)'
      });
    }

    // Basic base64 validation
    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
    if (!base64Pattern.test(img)) {
      return res.status(400).json({ error: 'Invalid base64 format' });
    }

    console.log('Processing image of length:', img.length);
    console.log('Image starts with:', img.substring(0, 50));

    const fileId = await uploadImage(img);
    console.log('✓ Upload successful, fileId:', fileId);

    res.json({
      success: true,
      fileId,
      message: 'Image uploaded successfully. Use this fileId to start analysis.',
      nextStep: 'POST /api/face/start-analysis with { "fileId": "' + fileId + '" }'
    });

  } catch (error) {
    console.error('Error in uploadImage:', error);
    res.status(500).json({ error: error.message });
  }
};

// Start analysis endpoint - Step 2 of the face analysis process
exports.startAnalysis = async (req, res) => {
  try {
    console.log('=== START ANALYSIS ENDPOINT ===');

    const { fileId } = req.body;
    if (!fileId) {
      return res.status(400).json({ error: 'fileId is required' });
    }

    console.log('Starting analysis for fileId:', fileId);

    const taskId = await startAnalysis(fileId);
    console.log('✓ Analysis started, taskId:', taskId);

    res.json({
      success: true,
      taskId,
      message: 'Analysis started successfully. Use this taskId to get results.',
      nextStep: 'GET /api/face/result/' + taskId + ' (poll this endpoint until analysis is complete)'
    });

  } catch (error) {
    console.error('Error in startAnalysis:', error);
    res.status(500).json({ error: error.message });
  }
};

// Get analysis result endpoint - Step 3 of the face analysis process
exports.getAnalysisResult = async (req, res) => {
  try {
    console.log('=== GET ANALYSIS RESULT ENDPOINT ===');

    const { taskId } = req.params;
    if (!taskId) {
      return res.status(400).json({ error: 'taskId is required' });
    }

    console.log('Getting result for taskId:', taskId);
    console.log('TaskId length:', taskId.length);
    console.log('TaskId encoded:', encodeURIComponent(taskId));

    // Try to get the analysis result (this will poll internally)
    const result = await getAnalysisResult(taskId);
    console.log('✓ Analysis completed successfully');

    res.json({
      success: true,
      result,
      message: 'Analysis completed successfully',
      taskId: taskId
    });

  } catch (error) {
    console.error('Error in getAnalysisResult:', error);

    // Provide more helpful error messages based on the specific error
    if (error.message.includes('Invalid Task Id') || error.message.includes('HTTP 400')) {
      res.status(400).json({
        error: 'Invalid Task ID',
        message: 'The task ID may have expired, be malformed, or not exist.',
        details: error.message,
        taskId: req.params.taskId,
        suggestions: [
          'Verify the task ID is correct and complete',
          'Check if the task ID has expired (tasks typically expire after some time)',
          'Try creating a new analysis task with /start-analysis endpoint'
        ]
      });
    } else if (error.message.includes('timeout') || error.message.includes('likely no face detected')) {
      res.status(422).json({
        error: 'Analysis timeout',
        message: 'Face analysis timeout - this usually means no clear face was detected in the image.',
        details: error.message,
        suggestions: [
          'Ensure the image contains a clear, well-lit face',
          'Try with a higher quality image',
          'Make sure the face is not too small or at an extreme angle'
        ]
      });
    } else if (error.message.includes('still running') || error.message.includes('processing')) {
      res.status(202).json({
        success: false,
        status: 'processing',
        message: 'Analysis is still in progress. Please try again in a few seconds.',
        details: error.message,
        taskId: taskId,
        retryAfter: 5
      });
    } else if (error.message.includes('Analysis failed')) {
      res.status(422).json({
        error: 'Analysis failed',
        message: 'The face analysis process failed.',
        details: error.message,
        taskId: taskId
      });
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message,
        taskId: taskId
      });
    }
  }
};

// Test endpoint to create a fresh task ID for testing
exports.createTestTask = async (req, res) => {
  try {
    console.log('=== CREATE TEST TASK ENDPOINT ===');

    // Create a simple test image (1x1 pixel red PNG in base64)
    const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

    console.log('Step 1: Uploading test image...');
    const fileId = await uploadImage(testImageBase64);
    console.log('✓ Upload successful, fileId:', fileId);

    console.log('Step 2: Starting analysis...');
    const taskId = await startAnalysis(fileId);
    console.log('✓ Analysis started, taskId:', taskId);

    res.json({
      success: true,
      message: 'Test task created successfully',
      fileId,
      taskId,
      testEndpoint: `/api/face/result/${taskId}`,
      note: 'You can now test the result endpoint with this fresh taskId. Note: This test image likely won\'t produce meaningful face analysis results.'
    });

  } catch (error) {
    console.error('Error creating test task:', error);
    res.status(500).json({ error: error.message });
  }
};

// Legacy combined endpoint - performs all three steps in one call
// DEPRECATED: Use the separate endpoints above for better control
// exports.analyzeFace = async (req, res) => {
//   try {
//     console.log('=== LEGACY COMBINED ANALYSIS ENDPOINT ===');
//     console.log('⚠️  DEPRECATED: Consider using separate endpoints for better control');

//     // Handle both data URL format and direct base64
//     let img = req.body.image;
//     if (!img) {
//       return res.status(400).json({ error: 'No image provided' });
//     }

//     // If it's a data URL, extract the base64 part
//     if (img.includes(',')) {
//       const parts = img.split(',');
//       if (parts.length !== 2) {
//         return res.status(400).json({ error: 'Invalid data URL format' });
//       }
//       img = parts[1];
//     }

//     // Validate base64 format
//     if (!img || img.length < 1000) {
//       return res.status(400).json({
//         error: 'Image too small - please provide a larger image with a clear face (minimum ~1KB)'
//       });
//     }

//     // Basic base64 validation
//     const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
//     if (!base64Pattern.test(img)) {
//       return res.status(400).json({ error: 'Invalid base64 format' });
//     }

//     console.log('Processing image of length:', img.length);

//     try {
//       // Step 1: Upload image
//       console.log('Step 1: Uploading image...');
//       const fileId = await uploadImage(img);
//       console.log('✓ Upload successful, fileId:', fileId);

//       // Step 2: Start analysis
//       console.log('Step 2: Starting analysis...');
//       const taskId = await startAnalysis(fileId);
//       console.log('✓ Analysis started, taskId:', taskId);

//       // Step 3: Get results
//       console.log('Step 3: Getting analysis results...');
//       const result = await getAnalysisResult(taskId);
//       console.log('✓ Analysis completed');

//       res.json({
//         success: true,
//         result,
//         fileId,
//         taskId,
//         message: 'Complete analysis finished successfully',
//         recommendation: 'Consider using separate endpoints (/upload, /start-analysis, /result/:taskId) for better control and error handling'
//       });

//     } catch (analysisError) {
//       // Provide more helpful error messages
//       if (analysisError.message.includes('timeout')) {
//         res.status(422).json({
//           error: 'Face analysis timeout - this usually means no clear face was detected in the image. Please ensure the image contains a clear, well-lit face.',
//           details: analysisError.message
//         });
//       } else {
//         throw analysisError; // Re-throw other errors
//       }
//     }
//   } catch (error) {
//     console.error('Error in analyzeFace:', error);
//     res.status(500).json({ error: error.message });
//   }
// };
