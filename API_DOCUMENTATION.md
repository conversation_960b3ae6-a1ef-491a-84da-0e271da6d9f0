# Face Analyzer API Documentation

## Overview
The Face Analyzer API provides separate endpoints for each step of the face analysis process, with enhanced image validation, preprocessing, and intelligent polling strategies to maximize success rates and provide detailed debugging information.

## ✨ New Features
- **Enhanced Image Validation**: Comprehensive validation with format detection, dimension checks, and quality analysis
- **Intelligent Image Preprocessing**: Automatic optimization for better face detection (resizing, contrast enhancement, format conversion)
- **Smart Polling Strategy**: Exponential backoff with fast initial polling and detailed progress tracking
- **Comprehensive Error Analysis**: Detailed failure analysis with actionable recommendations
- **Debug Endpoints**: Complete flow testing with step-by-step debugging information

## API Endpoints

### 1. Upload Image
**POST** `/api/face/upload`

Upload an image to get a fileId for analysis.

**Request Body:**
```json
{
  "image": "base64_encoded_image_data"
}
```

**Response (Success):**
```json
{
  "success": true,
  "fileId": "WDSWe0B99JH0nBVbcGIQwRMLVU0BKZABnHdY...",
  "message": "Image uploaded and preprocessed successfully. Use this fileId to start analysis.",
  "imageInfo": {
    "width": 800,
    "height": 600,
    "fileSizeKB": 45.2,
    "mimeType": "image/jpeg",
    "aspectRatio": "1.33"
  },
  "warnings": ["Large image dimensions (1920x1080). Consider resizing for better performance."],
  "preprocessing": "Image was optimized for better face detection",
  "nextStep": "POST /api/face/start-analysis with { \"fileId\": \"...\" }"
}
```

**Response (Validation Failed):**
```json
{
  "error": "Image validation failed",
  "details": ["Image too small (0.5KB). Minimum size: 1KB"],
  "warnings": ["Small image dimensions may affect face detection accuracy"],
  "message": "Image validation failed:\n• Image too small...\n\nFor best results, please provide:\n• A clear, well-lit photo of a face...",
  "imageInfo": {
    "width": 100,
    "height": 100,
    "fileSizeKB": 0.5,
    "mimeType": "image/png"
  }
}
```

### 2. Start Analysis
**POST** `/api/face/start-analysis`

Start face analysis using the fileId from the upload step.

**Request Body:**
```json
{
  "fileId": "WDSWe0B99JH0nBVbcGIQwRMLVU0BKZABnHdY..."
}
```

**Response:**
```json
{
  "success": true,
  "taskId": "WDSWe0B99JH0nBVbcGIQwRMLVU0BKZABnHdY_NjCRpL...",
  "message": "Analysis started successfully. Use this taskId to get results.",
  "nextStep": "GET /api/face/result/WDSWe0B99JH0nBVbcGIQwRMLVU0BKZABnHdY_NjCRpL... (poll this endpoint until analysis is complete)"
}
```

### 3. Get Analysis Result
**GET** `/api/face/result/:taskId`

Get the analysis result using the taskId from the start analysis step.

**URL Parameters:**
- `taskId`: The task ID returned from the start analysis endpoint

**Response (when complete):**
```json
{
  "success": true,
  "result": {
    "eyeShape": { /* analysis data */ },
    "faceShape": { /* analysis data */ },
    "lipShape": { /* analysis data */ }
  },
  "message": "Analysis completed successfully"
}
```

**Response (when still processing):**
```json
{
  "success": false,
  "status": "processing",
  "message": "Analysis is still in progress. Please try again in a few seconds."
}
```

## Usage Flow

### Step-by-Step Process
1. **Upload Image**: POST to `/api/face/upload` with base64 image data
2. **Start Analysis**: POST to `/api/face/start-analysis` with the received fileId
3. **Poll for Results**: GET `/api/face/result/:taskId` until analysis is complete

### Example Usage with JavaScript

```javascript
async function analyzeface(imageBase64) {
  try {
    // Step 1: Upload image
    const uploadResponse = await fetch('/api/face/upload', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ image: imageBase64 })
    });
    const uploadData = await uploadResponse.json();
    
    if (!uploadData.success) {
      throw new Error('Upload failed: ' + uploadData.error);
    }
    
    console.log('Image uploaded, fileId:', uploadData.fileId);
    
    // Step 2: Start analysis
    const analysisResponse = await fetch('/api/face/start-analysis', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ fileId: uploadData.fileId })
    });
    const analysisData = await analysisResponse.json();
    
    if (!analysisData.success) {
      throw new Error('Analysis start failed: ' + analysisData.error);
    }
    
    console.log('Analysis started, taskId:', analysisData.taskId);
    
    // Step 3: Poll for results
    let result;
    let attempts = 0;
    const maxAttempts = 60; // 5 minutes with 5-second intervals
    
    while (attempts < maxAttempts) {
      const resultResponse = await fetch(`/api/face/result/${analysisData.taskId}`);
      const resultData = await resultResponse.json();
      
      if (resultData.success) {
        result = resultData.result;
        break;
      } else if (resultData.status === 'processing') {
        console.log('Analysis in progress, waiting...');
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
        attempts++;
      } else {
        throw new Error('Analysis failed: ' + resultData.error);
      }
    }
    
    if (!result) {
      throw new Error('Analysis timeout after ' + maxAttempts + ' attempts');
    }
    
    console.log('Analysis complete:', result);
    return result;
    
  } catch (error) {
    console.error('Face analysis error:', error);
    throw error;
  }
}
```

## Debug and Testing Endpoints

### 4. Create Test Task
**POST** `/api/face/create-test-task`

Creates a test task with a minimal image for testing the API flow.

**Response:**
```json
{
  "success": true,
  "message": "Test task created successfully",
  "fileId": "...",
  "taskId": "...",
  "testEndpoint": "/api/face/result/...",
  "note": "You can now test the result endpoint with this fresh taskId."
}
```

### 5. Debug Complete Flow
**POST** `/api/face/debug-flow`

Tests the complete analysis flow with detailed debugging information and failure analysis.

**Request Body:**
```json
{
  "image": "base64_encoded_image_data"
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Complete flow executed successfully",
  "result": { /* face analysis results */ },
  "debugInfo": {
    "steps": [
      {
        "step": 1,
        "name": "Image Validation & Preprocessing",
        "status": "success",
        "duration": 150,
        "imageInfo": { /* image details */ },
        "warnings": []
      },
      {
        "step": 2,
        "name": "Image Upload",
        "status": "success",
        "duration": 1200,
        "fileId": "..."
      },
      {
        "step": 3,
        "name": "Analysis Start",
        "status": "success",
        "duration": 800,
        "taskId": "..."
      },
      {
        "step": 4,
        "name": "Analysis Results",
        "status": "success",
        "duration": 45000,
        "resultSummary": {
          "hasResults": true,
          "resultKeys": ["eyeShape", "faceShape", "lipShape"]
        }
      }
    ],
    "timing": {
      "validation": 150,
      "upload": 1200,
      "analysisStart": 800,
      "results": 45000,
      "total": 47150
    }
  },
  "recommendations": [
    "Address image quality warnings for better results"
  ]
}
```

**Response (Failure with Analysis):**
```json
{
  "success": false,
  "message": "Analysis failed but flow completed partially",
  "error": "Face analysis timeout after 61.9s - likely no face detected or image quality issues",
  "debugInfo": { /* detailed step information */ },
  "failureAnalysis": {
    "likelyReason": "face_detection_failed",
    "confidence": "high",
    "suggestions": [
      "The image likely does not contain a clear, detectable face",
      "Try with a different image that has a clear, well-lit face",
      "Ensure the face takes up at least 20% of the image area"
    ]
  },
  "recommendations": [
    "Try with a clearer image containing a single, well-lit face",
    "Ensure the face is the main subject and takes up a significant portion of the image"
  ]
}
```

## Legacy Endpoint (Deprecated)

### Combined Analysis
**POST** `/api/face/analyze`

This endpoint performs all three steps in one call but provides less control over the process.

**Request Body:**
```json
{
  "image": "base64_encoded_image_data"
}
```

**Note:** This endpoint is deprecated. Use the separate endpoints above for better error handling and control.

## Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
  "error": "No image provided"
}
```

**422 Unprocessable Entity:**
```json
{
  "error": "Face analysis timeout - this usually means no clear face was detected in the image. Please ensure the image contains a clear, well-lit face.",
  "details": "..."
}
```

**500 Internal Server Error:**
```json
{
  "error": "Internal server error message"
}
```

## Benefits of Separate Endpoints

1. **Better Error Handling**: Each step can fail independently with specific error messages
2. **Progress Tracking**: You can track which step failed and retry from that point
3. **Flexibility**: You can implement custom polling strategies for the result endpoint
4. **Debugging**: Easier to debug issues by isolating each step
5. **Scalability**: Can implement caching, rate limiting, or other optimizations per endpoint

## Image Requirements

- **Format**: Base64 encoded image data (JPEG, PNG supported)
- **Size**: Minimum 1KB for meaningful face analysis
- **Content**: Must contain a clear, well-lit human face
- **Quality**: Higher quality images produce better analysis results
