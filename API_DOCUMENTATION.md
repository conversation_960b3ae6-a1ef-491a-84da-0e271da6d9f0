# Face Analyzer API Documentation

## Overview
The Face Analyzer API now provides separate endpoints for each step of the face analysis process, giving you better control and error handling capabilities.

## API Endpoints

### 1. Upload Image
**POST** `/api/face/upload`

Upload an image to get a fileId for analysis.

**Request Body:**
```json
{
  "image": "base64_encoded_image_data"
}
```

**Response:**
```json
{
  "success": true,
  "fileId": "WDSWe0B99JH0nBVbcGIQwRMLVU0BKZABnHdY...",
  "message": "Image uploaded successfully. Use this fileId to start analysis.",
  "nextStep": "POST /api/face/start-analysis with { \"fileId\": \"...\" }"
}
```

### 2. Start Analysis
**POST** `/api/face/start-analysis`

Start face analysis using the fileId from the upload step.

**Request Body:**
```json
{
  "fileId": "WDSWe0B99JH0nBVbcGIQwRMLVU0BKZABnHdY..."
}
```

**Response:**
```json
{
  "success": true,
  "taskId": "WDSWe0B99JH0nBVbcGIQwRMLVU0BKZABnHdY_NjCRpL...",
  "message": "Analysis started successfully. Use this taskId to get results.",
  "nextStep": "GET /api/face/result/WDSWe0B99JH0nBVbcGIQwRMLVU0BKZABnHdY_NjCRpL... (poll this endpoint until analysis is complete)"
}
```

### 3. Get Analysis Result
**GET** `/api/face/result/:taskId`

Get the analysis result using the taskId from the start analysis step.

**URL Parameters:**
- `taskId`: The task ID returned from the start analysis endpoint

**Response (when complete):**
```json
{
  "success": true,
  "result": {
    "eyeShape": { /* analysis data */ },
    "faceShape": { /* analysis data */ },
    "lipShape": { /* analysis data */ }
  },
  "message": "Analysis completed successfully"
}
```

**Response (when still processing):**
```json
{
  "success": false,
  "status": "processing",
  "message": "Analysis is still in progress. Please try again in a few seconds."
}
```

## Usage Flow

### Step-by-Step Process
1. **Upload Image**: POST to `/api/face/upload` with base64 image data
2. **Start Analysis**: POST to `/api/face/start-analysis` with the received fileId
3. **Poll for Results**: GET `/api/face/result/:taskId` until analysis is complete

### Example Usage with JavaScript

```javascript
async function analyzeface(imageBase64) {
  try {
    // Step 1: Upload image
    const uploadResponse = await fetch('/api/face/upload', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ image: imageBase64 })
    });
    const uploadData = await uploadResponse.json();
    
    if (!uploadData.success) {
      throw new Error('Upload failed: ' + uploadData.error);
    }
    
    console.log('Image uploaded, fileId:', uploadData.fileId);
    
    // Step 2: Start analysis
    const analysisResponse = await fetch('/api/face/start-analysis', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ fileId: uploadData.fileId })
    });
    const analysisData = await analysisResponse.json();
    
    if (!analysisData.success) {
      throw new Error('Analysis start failed: ' + analysisData.error);
    }
    
    console.log('Analysis started, taskId:', analysisData.taskId);
    
    // Step 3: Poll for results
    let result;
    let attempts = 0;
    const maxAttempts = 60; // 5 minutes with 5-second intervals
    
    while (attempts < maxAttempts) {
      const resultResponse = await fetch(`/api/face/result/${analysisData.taskId}`);
      const resultData = await resultResponse.json();
      
      if (resultData.success) {
        result = resultData.result;
        break;
      } else if (resultData.status === 'processing') {
        console.log('Analysis in progress, waiting...');
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
        attempts++;
      } else {
        throw new Error('Analysis failed: ' + resultData.error);
      }
    }
    
    if (!result) {
      throw new Error('Analysis timeout after ' + maxAttempts + ' attempts');
    }
    
    console.log('Analysis complete:', result);
    return result;
    
  } catch (error) {
    console.error('Face analysis error:', error);
    throw error;
  }
}
```

## Legacy Endpoint (Deprecated)

### Combined Analysis
**POST** `/api/face/analyze`

This endpoint performs all three steps in one call but provides less control over the process.

**Request Body:**
```json
{
  "image": "base64_encoded_image_data"
}
```

**Note:** This endpoint is deprecated. Use the separate endpoints above for better error handling and control.

## Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
  "error": "No image provided"
}
```

**422 Unprocessable Entity:**
```json
{
  "error": "Face analysis timeout - this usually means no clear face was detected in the image. Please ensure the image contains a clear, well-lit face.",
  "details": "..."
}
```

**500 Internal Server Error:**
```json
{
  "error": "Internal server error message"
}
```

## Benefits of Separate Endpoints

1. **Better Error Handling**: Each step can fail independently with specific error messages
2. **Progress Tracking**: You can track which step failed and retry from that point
3. **Flexibility**: You can implement custom polling strategies for the result endpoint
4. **Debugging**: Easier to debug issues by isolating each step
5. **Scalability**: Can implement caching, rate limiting, or other optimizations per endpoint

## Image Requirements

- **Format**: Base64 encoded image data (JPEG, PNG supported)
- **Size**: Minimum 1KB for meaningful face analysis
- **Content**: Must contain a clear, well-lit human face
- **Quality**: Higher quality images produce better analysis results
